"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { ReportTypeSelector } from "@/components/reports/report-type-selector"
import { ReportFilters } from "@/components/reports/report-filters"
import { ReportsLibrary } from "@/components/reports/reports-library"
import { SF2ReportDialog } from "@/components/reports/sf2-report-dialog"
import { SF4ReportDialog } from "@/components/reports/sf4-report-dialog"
import { CustomReportDialog } from "@/components/reports/custom-report-dialog"
import { ReportScheduler } from "@/components/reports/report-scheduler"
import { ReportArchive } from "@/components/reports/report-archive"
import { ReportWizardDialog } from "@/components/reports/report-wizard-dialog"
import { <PERSON>ulkReportGenerator } from "@/components/reports/bulk-report-generator"
import { ReportType, ReportFilters as ReportFiltersType, ExportFormat } from "@/lib/types/reports"
import { mockGeneratedReports, mockReportAnalytics } from "@/lib/data/reports-mock-data"
import {
  FileText,
  Download,
  Calendar,
  Users,
  Plus,
  BarChart3,
  Clock,
  TrendingUp,
  Settings
} from "lucide-react"
import { toast } from "sonner"

export default function ReportsPage() {
  const [selectedReportType, setSelectedReportType] = useState<ReportType>("SF2")
  const [reportFilters, setReportFilters] = useState<ReportFiltersType>({})
  const [activeTab, setActiveTab] = useState("generate")
  const [showSF2Dialog, setShowSF2Dialog] = useState(false)
  const [showSF4Dialog, setShowSF4Dialog] = useState(false)
  const [showCustomDialog, setShowCustomDialog] = useState(false)
  const [showWizardDialog, setShowWizardDialog] = useState(false)

  // Mock analytics data
  const totalReports = mockGeneratedReports.length
  const readyReports = mockGeneratedReports.filter(r => r.status === "READY").length
  const totalDownloads = mockGeneratedReports.reduce((sum, r) => sum + r.downloadCount, 0)
  const scheduledReports = 8 // Mock data

  const handleGenerateReport = () => {
    if (selectedReportType === "SF2") {
      setShowSF2Dialog(true)
    } else if (selectedReportType === "SF4") {
      setShowSF4Dialog(true)
    } else if (selectedReportType === "CUSTOM") {
      setShowCustomDialog(true)
    } else {
      toast.success("Report generation started", {
        description: "Your report will be ready in a few minutes"
      })
    }
  }

  const handleDownload = (reportId: string, format: ExportFormat) => {
    toast.success(`Downloading report in ${format} format`)
  }

  const handlePreview = (reportId: string) => {
    toast.info("Opening report preview")
  }

  const handleDelete = (reportId: string) => {
    toast.success("Report deleted successfully")
  }

  const handleArchive = (reportId: string) => {
    toast.success("Report archived successfully")
  }

  const handleShare = (reportId: string) => {
    toast.success("Report sharing link copied to clipboard")
  }

  const handleRegenerate = (reportId: string) => {
    toast.success("Report regeneration started")
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports Dashboard</h1>
          <p className="text-muted-foreground">
            Generate Philippine DepEd forms and custom attendance reports
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setShowWizardDialog(true)}>
            <Settings className="mr-2 h-4 w-4" />
            Wizard
          </Button>
          <Button onClick={handleGenerateReport}>
            <Plus className="mr-2 h-4 w-4" />
            New Report
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalReports}</div>
            <p className="text-xs text-muted-foreground">
              {readyReports} ready to download
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Downloads</CardTitle>
            <Download className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDownloads}</div>
            <p className="text-xs text-muted-foreground">
              Total downloads
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Scheduled Reports</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{scheduledReports}</div>
            <p className="text-xs text-muted-foreground">
              Auto-generated
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">DepEd Compliance</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">100%</div>
            <p className="text-xs text-muted-foreground">
              SF2 & SF4 ready
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="generate">Generate Report</TabsTrigger>
          <TabsTrigger value="library">Reports Library</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled</TabsTrigger>
          <TabsTrigger value="archive">Archive</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="generate" className="space-y-6">
          <div className="grid gap-6 lg:grid-cols-3">
            <div className="lg:col-span-2">
              <ReportTypeSelector
                selectedType={selectedReportType}
                onTypeSelect={(type) => {
                  setSelectedReportType(type)
                  if (type === "SF2") {
                    setShowSF2Dialog(true)
                  } else if (type === "SF4") {
                    setShowSF4Dialog(true)
                  } else if (type === "CUSTOM") {
                    setShowCustomDialog(true)
                  }
                }}
              />
            </div>
            <div>
              <ReportFilters
                filters={reportFilters}
                onFiltersChange={setReportFilters}
                reportType={selectedReportType}
              />
            </div>
          </div>

          {/* Generate Button */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium">Ready to Generate</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedReportType} report with {Object.keys(reportFilters).length} filters applied
                  </p>
                </div>
                <Button size="lg" onClick={handleGenerateReport}>
                  <FileText className="mr-2 h-5 w-5" />
                  Generate Report
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="library" className="space-y-6">
          <ReportsLibrary
            reports={mockGeneratedReports}
            onDownload={handleDownload}
            onPreview={handlePreview}
            onDelete={handleDelete}
            onArchive={handleArchive}
            onShare={handleShare}
            onRegenerate={handleRegenerate}
          />
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-6">
          <ReportScheduler />
        </TabsContent>

        <TabsContent value="archive" className="space-y-6">
          <ReportArchive />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Report Analytics</CardTitle>
              <CardDescription>
                View usage statistics and performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Analytics dashboard coming soon</p>
                <p className="text-sm">Track report generation, downloads, and usage patterns</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* SF2 Report Dialog */}
      <SF2ReportDialog
        open={showSF2Dialog}
        onOpenChange={setShowSF2Dialog}
        config={{
          id: `temp-${Date.now()}`,
          name: `SF2 Report - ${selectedReportType}`,
          type: selectedReportType,
          description: "SF2 Daily Attendance Report",
          dateRange: {
            startDate: new Date().toISOString().split('T')[0],
            endDate: new Date().toISOString().split('T')[0]
          },
          filters: reportFilters,
          settings: {
            includeSignatures: true,
            pageOrientation: "portrait"
          },
          createdBy: "current-user",
          createdAt: new Date().toISOString(),
          lastModified: new Date().toISOString()
        }}
      />

      {/* SF4 Report Dialog */}
      <SF4ReportDialog
        open={showSF4Dialog}
        onOpenChange={setShowSF4Dialog}
        config={{
          id: `temp-${Date.now()}`,
          name: `SF4 Report - ${selectedReportType}`,
          type: selectedReportType,
          description: "SF4 Monthly Learner's Movement Report",
          dateRange: {
            startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
            endDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString().split('T')[0]
          },
          filters: reportFilters,
          settings: {
            includeSignatures: true,
            pageOrientation: "landscape"
          },
          createdBy: "current-user",
          createdAt: new Date().toISOString(),
          lastModified: new Date().toISOString()
        }}
      />

      {/* Custom Report Dialog */}
      <CustomReportDialog
        open={showCustomDialog}
        onOpenChange={setShowCustomDialog}
      />
    </div>
  )
}
